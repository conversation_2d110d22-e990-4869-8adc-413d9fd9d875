// API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  timestamp: string;
}

// Paginated Response wrapper
export interface PaginatedResponse<T> {
  content: T[];
  page: number;
  size: number;
  totalElements: number;
  totalPages: number;
  first: boolean;
  last: boolean;
  empty: boolean;
  numberOfElements: number;
}

// Financial Instrument
export interface Instrument {
  symbol: string;
  name: string;
  type: string; // InstrumentType enum as string
  marketCap?: number;
  country?: string;
  ipoYear?: number;
  sector?: string;
  industry?: string;
}

// OHLCV Data
export interface OHLCV {
  symbol: string;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  // Technical Indicators
  bollingerMiddle?: number;
  bollingerStdDev?: number;
  bollingerUpper?: number;
  bollingerLower?: number;
  dmiPlusDi?: number;
  dmiMinusDi?: number;
  dmiDx?: number;
  dmiAdx?: number;
}

// Position
export interface Position {
  id: number;
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice?: number;
  marketValue?: number;
  unrealizedPnl?: number;
  unrealizedPnlPercent?: number;
  entryDate: string;
  lastTradeDate?: string;
  stopLoss?: number;
  takeProfit?: number;
  riskAmount?: number;
  positionSize?: number;
  bollingerSignal?: string;
  createdAt: string;
  updatedAt: string;
}

// Watch List Item
export interface WatchListItem {
  id: number;
  displayIndex: number;
  symbol: string;
  startDate: string; // LocalDate as ISO string
  remarks?: string;
  oneMonthPerf?: number;
  threeMonthPerf?: number;
  sixMonthPerf?: number;
  createdDate: string; // LocalDateTime as ISO string
  updatedDate: string; // LocalDateTime as ISO string
}

// Watch List Request Types
export interface CreateWatchListRequest {
  displayIndex: number;
  symbol: string;
  startDate: string; // ISO date string
  remarks?: string;
}

export interface UpdateWatchListRequest {
  displayIndex?: number;
  remarks?: string;
  oneMonthPerf?: number;
  threeMonthPerf?: number;
  sixMonthPerf?: number;
}

export interface ReorderWatchListRequest {
  idToIndexMap: Record<number, number>;
}

export interface RecalculatePerformanceResponse {
  totalItems: number;
  successfulUpdates: number;
  failedUpdates: number;
  skippedItems: number;
  successfulSymbols: string[];
  failedSymbols: string[];
  skippedSymbols: string[];
  processingTimeMs: number;
  summaryMessage?: string;
}

// Process Information
export interface ProcessInfo {
  processId: string;
  processType: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  progress?: number;
  message?: string;
  metadata?: Record<string, any>;
}

// Technical Indicator Calculation Request
export interface TechnicalIndicatorRequest {
  period?: number;
  stdDevMultiplier?: number;
  minDataPoints?: number;
  startIndex?: number;
  endIndex?: number;
  maxSymbols?: number;
  symbols?: string[];
  dryRun?: boolean;
  calculationMode?: 'INCREMENTAL' | 'FULL_RECALCULATION' | 'SKIP_EXISTING';
  calculationMethod?: 'PURE_JAVA' | 'HYBRID_SQL_JAVA';
}

// Refresh Request
export interface RefreshAllRequest {
  dryRun: boolean;
  maxSymbols?: number;
  skipExisting: boolean;
  startIndex?: number;
  endIndex?: number;
}

// Validation Request
export interface ValidationRequest {
  dryRun: boolean;
  maxSymbols?: number;
  removeInvalidSymbols: boolean;
  validateMarketCap: boolean;
  minMarketCap?: number;
}

// CSV Upload Response
export interface CsvUploadResponse {
  totalRows: number;
  processedRows: number;
  skippedRows: number;
  errorRows: number;
  validationErrors: string[];
  summary: string;
}

// Sync Response
export interface SyncResponse {
  totalSecInstruments: number;
  newInstruments: number;
  existingInstruments: number;
  errors: number;
  summary: string;
}

// Chart Data Point
export interface ChartDataPoint {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  bollingerUpper?: number;
  bollingerMiddle?: number;
  bollingerLower?: number;
}

// Bulk Operation Interfaces
export interface BulkUpdateRequest {
  symbols: string[];
  startDate?: string;
  endDate?: string;
  dryRun?: boolean;
}

export interface BulkUpdateProgress {
  symbol: string;
  status: 'pending' | 'processing' | 'success' | 'error';
  message?: string;
  error?: string;
}

export interface BulkUpdateResult {
  totalSymbols: number;
  successCount: number;
  errorCount: number;
  results: BulkUpdateProgress[];
  summary: string;
}
